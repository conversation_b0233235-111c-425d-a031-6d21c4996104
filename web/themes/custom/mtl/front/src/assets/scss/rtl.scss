$dir: rtl; // ou conditionner la valeur selon la langue ou la configuration

@import 'vendors/directional';

.jumbotron {
  direction: $dir;
  text-align: if($dir == 'rtl', 'right', 'left');
  margin: 0 1em 0 2em !important;
  padding-left: if($dir == 'rtl', 1em, 0);
}

html[dir="rtl"] body {
    font-family: 'Cairo', sans-serif;
}
html[dir="rtl"] {
    .boc-light {
        // font-family: 'Cairo', sans-serif !important;
        font: bold 16px/1.2 'Cairo', sans-serif !important;
    }
    .boc-light .organigram-name{
        font: bold 16px/1.2 'Cairo', sans-serif !important;
    }
    .boc-light .organigram-title{
        font: bold 16px/1.2 'Cairo', sans-serif !important;
    }


    .btn i {
        @include transform-end(180deg);
        position: relative;
        top: 1px;
    }
    .share-links {
        i {
             @include transform-end(0);
        }
    }
    .btn .fa-download {
        @include transform-end(0);
    }
    .sliderHeader .wraper-btn {
        justify-content: flex-start !important;
    }

    //Header
    .header {
        &__bottom {
            &--overlay {
                & > form {
                    .form-item {
                        input[type="search"], input[type="text"] {
                            padding-left: 64px;
                            padding-right: 20px;
                        }
                    }
                }
            }
            @media only screen and (max-width: 992px) {
                .logo {
                    left: auto;
                    right: 50%;
                    transform: translateX(50%);
                }
            }
            @media (max-width: 480px) {
                .logo {
                    right: 44%;
                }
                &--mainMenu {
                    nav[role="navigation"] {
                        ul.langue-switcher {
                            &:before {
                                left: 20px !important;
                            }
                            li {
                                padding: 10px 10px 10px 18px;
                                // &:first-child {
                                //     padding-right: 0;
                                //     padding-left: 0;
                                // }
                            }
                        }
    
                    }
                }
            }
        }
        
    }

    //Agenda
    .itemsWrapper {
        .bloc-agenda {
            .btn-wrapper {
                justify-content: flex-start;
                @media(max-width: 768px) {
                    justify-content: center;
                }
            }
        }
    }

    //Bloc E-service
    .e-services {
        &__content {
            .swiper-services {
                .swiper-button-prev, .swiper-rtl .swiper-button-next {
                    transform: rotate(180deg);
                }
                .swiper-button-next, .swiper-rtl .swiper-button-prev {
                    transform: rotate(0deg); 
                }
            }
        }
    }

    //Presentation du secteur
    .presentation {
        .tabs-button {
           transform: translateX(-150%);
        }
    }
    .window_scroll .presentation .tabs-button {
        transform: translateX(0);
    }
    .thumbsSlider-secteur {
        .onglets {
            .swiper-onglet {
                .swiper-slide {
                    a {
                        padding-left: 45px !important;
                    }
                }
                .swiper-button-prev, .swiper-rtl .swiper-button-next {
                    transform: rotate(180deg);
                }
                .swiper-button-next, .swiper-rtl .swiper-button-prev {
                    transform: rotate(0deg);
                }
            }
            .contentsOnglet {
                .swiper-slide {
                    .content {
                        margin-right: 40px;
                        margin-left: 10px;
                    }
                }
            }
        }
    }

    //Programme

    .organigrame__membres--two .card:last-child:before, 
    .organigrame__membres--tree .card:last-child:before {
        transform: translateX(100%);
    }
    .organigrame.organisation .organigrame__membres--four .card:nth-child(1)::before {
        transform: translateX(-100%);
    }
    .organigrame.organisation .organigrame__membres--four .card:nth-child(2)::before {
         transform: translateX(0);
    }


    // Lang
    .header__top--lang ul {
        padding: 0 15px 0 25px;
    }
    .header__top--lang {
        @media(max-width:480px) {
            margin-right: 0;
        }
    }

    //Presentation
    .bloc-odd {
        display: flex;
    }
    .h2-title {
        font-weight: 700;
    }

    .keyfigure-counter {
        direction: ltr;
    }
}

