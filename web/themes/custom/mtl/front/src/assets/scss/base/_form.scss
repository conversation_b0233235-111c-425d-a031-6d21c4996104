//sup Filter
body.suppFilter form.views-exposed-form .fieldgroup {
  display: none !important;
}
body.suppFilter #views-exposed-form-e-services-page-1 .fieldgroup {
  display: block !important;
}

form#views-exposed-form-actualite-page-1 {
  @media(max-width: 600px) {
    .form-radios.form--inline {
      margin-bottom: 0;
    }
    fieldset[id^="edit-field-type-d-actualites-target-id"] {
      padding-bottom: 85px;
    }
  }
}

//newletter
#webform-submission-newsletter-add-form {
    margin: 30px auto;
    .form-item-email {
      width: 90%;
      margin: 0 auto 20px;
    }
    input[type="submit"] {
      width: 90%;
    }
    @media(min-width:768px) {
      .form-item-email {
        width: 50%;
      }
      input[type="submit"] {
        width: 30%;
      }
    }
}

//Procedure && form
form#views-exposed-form-procedure-formulaire-page-1 {
  @media(max-width:600px) {
    .form-radios.form--inline {
      margin-bottom: 0;
    }
  }
  fieldset[id^="edit-field-domaines-activite-pf-target-id"] {
    @media(max-width:992px) {
      margin-top: 190px;
    }
    @media(max-width:768px) {
      margin-top: 240px;
    }
    @media(max-width:600px) {
      margin-top: 310px;
      .input-wrapper {
        margin-top: 30px;
      }
    }
  }
  .proc-form & {
    fieldset[id^="edit-field-type-de-procedure-target-id"] {
      display: block !important;
    }
  }
}