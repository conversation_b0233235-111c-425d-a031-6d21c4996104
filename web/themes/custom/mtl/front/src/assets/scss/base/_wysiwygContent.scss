.card-body {
    padding-left: 0;
    padding-right: 0;
}
.card {
    cursor: default;
}
p, figcaption {
    font-family: $quicksand-regular;
    font-size: 16px;
    line-height: rem(28);
    letter-spacing: .3px;
    margin-bottom: 30px;
    html[dir="rtl"] & {
        font-family: "Cairo", sans-serif;
        // font-weight: 400;
    }
}
p {
    margin-bottom: 15px;
}
p + ul {
    padding: 0 30px !important;
    margin-bottom: 10px;
}
figcaption {
    margin-bottom: 0;
    display: flex;
    justify-content: center;
}
a {
    text-decoration: underline;
    color: $black;
    transition: all .2s ease;
    letter-spacing: .3px;
    &:hover {
        text-decoration: none !important;
    }
}
img {   
    max-width: 100%;
    border-radius: 50px;
    padding: 30px 0;
}
.wys-img {
    margin: 0 auto 10px;
    img {
        padding: 0;
        border-radius: 10px;
    }
}
h2,h3 {
    font-family: $rubik-bold;
    font-weight: bold;
    letter-spacing: .3px;
    // text-transform: uppercase;
    color: $primary;
    margin-bottom: 20px;
    html[dir="rtl"] & {
        font-family: "Cairo", sans-serif;
    }
}
h2 {
    font-size: rem(30);
    // #first-letter {
    //     position: relative;
    //     bottom: 0;
    //     text-transform: uppercase;
    //     font-size: inherit;
    //     color: inherit;
    //     font-family: inherit;
    // }
}
h3 {
    font-size: rem(24);
}
h3 + div {
    h3 {
        padding: 10px 0;
        &:after {
            display: none;
        }
    }
}
h4 {
    font-size: rem(20);
    text-transform: none;
    margin-bottom: 10px;
}
ul, ol {
    li {
        font-family: $quicksand-regular;
        font-size: 16px;
        position: relative;
        // padding-left: 25px;
        @include start(padding, 25px);
        letter-spacing: .3px;
        html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
            font-weight: 400;
        }
        &:before , &::after {
            content: "";
            position: absolute;
            background: $primary;

        }
        &:before {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            // left: 0;
            @include start(position, 0);
            top: 6px;
        }
        &:after {
            width: 1px;
            height: calc(100% - 16px);
            // left: 6px;
            @include start(position, 6px);
            top: 20px;
        }
        
        &:not(:last-child) {
            padding-bottom: rem(16);
        }
        &:last-child {
            &:after {
                display: none;
            }
        }
        ul {
            padding-top: 10px !important;
            padding-bottom: 10px !important;
        }
    }
}
ol {
    counter-reset: list-counter;
    list-style: none;
    padding: 0;
    margin: 0;
    li {
        position: relative;
        counter-increment: list-counter;
        // padding-left: 20px;
        @include start(padding, 20px);
        &::marker {
            display: none;
        }
        &:after {
            display: none;
        }
        &:before {
            content: counter(list-counter) ". ";
            font-family: $quicksand-bold;
            width: auto;
            height: auto;
            top: 0;
            background: transparent;
            color: $primary;
             html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
            font-weight: 700;
            }
        }
    }
}

.grp-btn {
    gap: 15px;
    .btn {
        font-family: $rubik-regular;
        background: rgba(#3C77CE, .10);
        color: $primary;
        font-size: 14px;
        text-decoration: none;
        text-transform: none;
        border: 0;
        transition: all 400ms ease;
        pointer-events: none;
        html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
        }
    }
}
.btn {
    font-family: $rubik-bold;
    font-weight: bold;
    text-decoration: none;
    color: $white;
    padding-left: 20px;
    padding-right: 20px;
    html[dir="rtl"] & {
        font-family: "Cairo", sans-serif;
    }
 }
.external-link {
    .btn {
        position: relative;
       text-decoration: none;
       color: $white;
       padding-right: 40px;
        &:before {
            content: "";
            mask: url($images-path + 'icons/icon-external-link.svg') no-repeat 0 0;
            width: 12px;
            height: 12px;
            background: $white;
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}
.btn-rsociaux, .social-media-sharing {
    width: 100%;
    h3 {
        margin-bottom: 20px;
    }
    ul {
        display: flex;
        flex-wrap: wrap;
        gap: 0 10px;
        margin-bottom: 0;
        li {
            padding-left: 0;
            padding-right: 0;
            margin-right: 0;
            &:before, &:after {
                display: none;
            }
            a {
                position: relative;
                font-size: 0;
                width: 48px;
                height: 48px;
                border-radius: 15px;
                border: 1px solid $black;
                display: block;
                &:before {
                    content: "";
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: $black;
                    width: 20px;
                    height: 20px;
                    transition: all 450ms ease;
                }
                &.facebook {
                    &:before {
                        mask: url($images-path + 'icons/icon-facebook.svg') no-repeat 0 0;
                        width: 11px;
                        height: 22px;
                    }
                }
                &.twitter {
                    &:before {
                        mask: url($images-path + 'icons/icon-x-twitter.svg') no-repeat 0 0;
                    }
                }
                &.whatsap {
                    &:before {
                        mask: url($images-path + 'icons/icon-whatsap.svg') no-repeat 0 0;
                    }
                }  
                &.doc {
                    &:before {
                        mask: url($images-path + 'icons/icon-doc.svg') no-repeat 0 0;
                        width: 27px;
                    }
                }
                &.linkedin {
                    &:before {
                        mask: url($images-path + 'icons/icon-linkedin.svg') no-repeat 0 0;
                        width: 27px;
                    }
                }
                &.instagram {
                    &:before {
                        mask: url($images-path + 'icons/icon-instagram.svg') no-repeat 0 0;
                    }
                }
                &.youtube {
                    &:before {
                        mask: url($images-path + 'icons/icon-youtube.svg') no-repeat 0 0;
                    }
                }
                &.email {
                    &:before {
                        mask: url($images-path + 'icons/mail-line.svg') no-repeat 0 0;
                        width: 27px;
                    }
                }
                &.print {
                    &:before {
                        mask: url($images-path + 'icons/icon-printer.svg') no-repeat 0 0;
                    }
                }
                &:hover {
                    background: $primary;
                    border-color: $primary;
                    &::before {
                        background: $white;
                    }
                }
            }
            &:last-child {
                a {
                    &::before {
                        display: none;
                    }
                }
            }
        }
    }
    .addtoany_list .addtoany_share{
        position: relative;
        font-size: 0;
        width: 48px;
        height: 48px;
        border-radius: 15px;
        border: 1px solid #111111;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
            background: $primary;
            border-color: $primary;
            span {
                opacity: 1;
                &:after {
                    color: $white !important;
                }
            }
        }
    }
    .addtoany_list.a2a_kit_size_32 a > span{
      background: transparent !important;
      position: relative;
      &:after {
            content: "+";
            position: absolute;
            font-family: $rubik-bold;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 32px;
            color: $black;
            font-weight: bold;
            pointer-events: none;
      }
        html[dir="rtl"] & {
            &:after {
                font-family: "Cairo", sans-serif;
            }
        }
    
    }
    .addtoany_list.a2a_kit_size_32 a > span svg{
        display: none;
    }
    .addtoany_list.a2a_kit_size_32 a > span svg path{
        background: #3C77CE;
        fill: $black;
    }
    &.ministre {
        ul {
            li {
                a {
                    width: 20px;
                    height: 20px;
                    border-radius: 0;
                    border-color: transparent;
                    &:before {
                        background: $primary;
                    }
                    &:hover {
                        background: transparent;
                        border-color: transparent;
                        &::before {
                            background: $black;
                        }
                    }
                }
                
            }
        } 
    }
    @media(max-width:600px) {
        padding-top: 10px;
    }
}

.signature-box {
    font-family: $quicksand-bold;
    position: relative;
    margin-left: auto;
    text-align: right;
    font-size: 16px;
     html[dir="rtl"] & {
      font-family: "Cairo", sans-serif;
      font-weight: 700;
    }
    small {
        font-family: $quicksand-regular;
        font-size: 16px;
        html[dir="rtl"] & {
            font-family: "Cairo", sans-serif;
            font-weight: 400;
        }
    }
}
.bloc-wysi {
    border-radius: 14px;
    background: $white;
    padding: 30px;
    box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
}
figure[role="group"] {
    img {
        padding: 0;
        border-radius: 0;
        display: block;
        margin: 0 auto;
    }
}

//Table
table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 10px;
    overflow: hidden;
    table-layout: fixed;
    padding: 0 30px;
}
table thead th {
    background-color: $primary;
    color: $white;
    font-weight: bold;
}
th,
  td {
    font-family: $quicksand-regular;
    padding: 15px;
    text-align: center;
    font-size: 16px;
    html[dir="rtl"] & {
        font-family: "Cairo", sans-serif;
        font-weight: 400;
    }
}
th {
    font-family: $quicksand-bold;
    font-size: 18px;
    html[dir="rtl"] & {
        font-family: "Cairo", sans-serif;
        font-weight: bold;
    }
}
tbody tr td:nth-child(1) {
    font-family: $quicksand-bold;
    color: $primary;
    html[dir="rtl"] & {
        font-family: "Cairo", sans-serif;
        font-weight: bold;
    }
}
tbody tr:nth-child(odd) {
    background-color: $white;
}
tbody tr:nth-child(even) {
    background-color: #F3F8FF;
}
.accord-wysiw {
    box-shadow: rgba(0, 0, 0, .06) 0px 4px 8px 0px;
    h3 {
       margin-bottom: 0;
    }
}

body.page-content &{
    img {
        padding: 0;
        margin: 30px auto;
        border-radius: 14px;
    }
    .accord-wysiw {
        & > h3 {
            margin-bottom: 0;
            padding-top: 20px;
            padding-bottom: 20px;
            padding-right: 90px;
            color: $black;
            font-size: 20px;
            &.active {
                color: $primary;
            }
            &~h3 {
                color: $primary;
            }
        }
        p {
            &:last-of-type {
                margin-bottom: 0;
            }
        }
        ul.menu {
            padding: 0;
            margin: 10px;
            box-shadow: none;
            li {
                br {
                    display: none;
                }
            }
        }
    }
    ul, ol {
        background: $white;
        // border-radius: 14px;
        padding: 30px;
        // box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
        li {
            ul, ol {
                box-shadow: none;
            }
        }
    }
    ol {
        margin-bottom: 40px;
    }
    span.addtoany_list{
        a {
            margin-bottom: 30px;
        }
    }
    table {
        margin-bottom: 40px;
        @media(max-width: 767px) {
            padding-bottom: 10px;
            display: block;
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-color: #3C77CE transparent;
            scrollbar-width: thin;
        }
      
    }
    .e-services-wysiw {
        img {
            margin: 0;
            padding: 0;
        }
        .swiper-slide {
            height: 100%;
        }
    }
    & > div + span.addtoany_list {
        display: none;
    }
}
@media(max-width: 600px) {
    h2 {
        font-size: 24px;
        margin-bottom: 5px;
    }
    p {
        margin-bottom: 15px;
    }
    .signature-box {
        font-size: 12px;
        small {
            font-size: 12px;
        }
    }
}
#coop-bil + img {
    height: 460px;
    width: 100%;
    border-radius: 15px;
    padding: 0;
    margin: 0px 0 15px;
}