.chiffres-cles {
    width: 100%;
    background: $primary;
    padding: rem(50) 0;
    @media(max-width:1024px) {
        padding-bottom: 20px;
    }
    @media(max-width:767px) {
        .container{
          max-width: 100%;
        }
    }
    @media(max-width:640px) {
        padding: 35px 0;
    }

    .swiper.chiffre-cles{
        padding-bottom: 80px;
        // @media(max-width:1024px) {
        //     padding-bottom: 80px;
        // }
        .swiper-slide {
            height: initial !important;
        }
        .slide-content {
            background: #598bd5;
            border-radius: 10px;
            padding: 40px 20px 20px;
            border: 1px solid rgba($white, .5);
            // height: 100%;
            text-align: center;
            box-shadow: 0 10px 12px rgba(0, 0, 0, .016);
            height: 100%;
            p, span {
                color: white;
            }
            span {
                display: block;
                font-family: $rubik-bold;
                font-weight: bold;
                font-size: rem(30);
                padding: 20px 0 10px;
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                }
            }
            img {
                transform: scale(1) !important;
            }
            @media(max-width:480px) {
                p {
                    word-break: break-all;
                }
                span {
                    font-size: 16px;
                }
            }
        }
        @include swiper-dots;
    }
}