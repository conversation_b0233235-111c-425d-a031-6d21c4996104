.reglementation {
    width: 100%;
    form#views-exposed-form-reglementation-page-1 {
        margin-bottom: 20px;
    }
    .card {
        padding: 30px 45px;
        height: 100%;
        img {
            width: 46px;
        }
        .card-body {
            padding: 0;
            
            p, h2 {
                font-family: $rubik-bold;
                font-size: 20px;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                max-height: 4.5em;
                transition: max-height 1300ms ease-in-out, -webkit-line-clamp 1300ms step-end;
                cursor: pointer;
                html[dir="rtl"] & {
                    font-family: "Cairo", sans-serif;
                    font-weight: 700;
                }
                &:hover {
                    -webkit-line-clamp: 999;
                    max-height: 100em;
                }
            }
            h2 {
                 display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            & > div {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: rem(15) 0;
                flex-wrap: wrap;
                gap: 10px;
                a {
                    font-family: $quicksand-regular;
                    background: #ebf1fa;
                    color: $primary;
                    text-transform: none;
                    font-size: 16px;
                    padding: 14px 18px;
                    border-width: 0;
                    box-shadow: none;
                    line-height: 1.2;
                    zoom: .85;
                    pointer-events: none;
                    html[dir="rtl"] & {
                        font-family: "Cairo", sans-serif;
                        font-weight: 400;
                    }
                    i {
                        animation: none;
                    }
                    &:hover {
                        background: $primary;
                        color: $white;
                    }
                    body.proc-form & {
                        pointer-events: none;
                    }
                }
            }
            & > a {
                text-transform: none;
                // height: 50px;
                min-width: 226px;
                .fa-download {
                    padding-right: 4px;
                }
            }
        }
        @media(max-width:600px) {
            padding: 30px;
        }
    }
    &.secteur {
        .card {
            cursor: pointer;
            .card-body {
                padding: 20px 0;
                & > div {
                    a {
                        pointer-events: all;
                    }
                }
            }
            &:hover {
                .card-body {
                    p {
                        color: $primary;
                    }
                }
            }
        }
    }
    &.presse {
        .card {
            padding: 30px 45px;
            height: 100%;
            .card-body {
                & > div {
                    align-items: flex-start;
                    gap: 50px;
                    flex-wrap: nowrap;
                    a {
                        font-family: $rubik-bold;
                        font-weight: bold;
                        background: $secondary;
                        color: $white;
                        border-width: 1px;
                        border-color: $secondary;
                        zoom: 1;
                        min-width: rem(226);
                        pointer-events: all;
                        html[dir="rtl"] & {
                            font-family: "Cairo", sans-serif;
                        }
                        &:hover {
                            background: transparent;
                            color: $secondary;
                        }
                    }
                    @media(max-width:768px) {
                        flex-wrap: wrap;
                        gap: 20px;
                    }
                }
            }
            
        }
    }
    //sup Secteur
    .suppSecteur & {
        form {
            fieldset[id^="edit-field-secteur-target-id--"]  {
               display: none !important; 
            }
             
        }
    }
}

@keyframes revealText {
    from {
        mask-size: 100% 4.5em;
        -webkit-mask-size: 100% 4.5em;
    }
    to {
        mask-size: 100% 100%;
        -webkit-mask-size: 100% 100%;
    }
}