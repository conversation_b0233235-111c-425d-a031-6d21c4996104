<?php

namespace Drupal\org_json_importer\Form;

use <PERSON>upal\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\file\Entity\File;
use Dr<PERSON>al\taxonomy\Entity\Vocabulary;
use Drupal\org_json_importer\Service\JsonParser;
use Drupal\org_json_importer\Service\TaxonomyImporter;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Form for importing organizational JSON data.
 */
class OrgJsonImportForm extends FormBase {

  /**
   * The JSON parser service.
   *
   * @var \Drupal\org_json_importer\Service\JsonParser
   */
  protected $jsonParser;

  /**
   * The taxonomy importer service.
   *
   * @var \Drupal\org_json_importer\Service\TaxonomyImporter
   */
  protected $taxonomyImporter;

  /**
   * Constructs a new OrgJsonImportForm object.
   */
  public function __construct(JsonParser $json_parser, TaxonomyImporter $taxonomy_importer) {
    $this->jsonParser = $json_parser;
    $this->taxonomyImporter = $taxonomy_importer;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('org_json_importer.json_parser'),
      $container->get('org_json_importer.taxonomy_importer')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'org_json_import_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['description'] = [
      '#markup' => '<p>' . $this->t('Upload a JSON file containing organizational hierarchy data to import as taxonomy terms.') . '</p>',
    ];

    $form['json_file'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('JSON File'),
      '#description' => $this->t('Select a JSON file to import organizational data.'),
      '#required' => TRUE,
      '#upload_validators' => [
        'file_validate_extensions' => ['json'],
        'file_validate_size' => [5 * 1024 * 1024], // 5MB limit
      ],
      '#upload_location' => 'temporary://',
    ];

    // Get existing vocabularies for selection
    $vocabularies = Vocabulary::loadMultiple();
    $vocab_options = [];
    foreach ($vocabularies as $vocab) {
      $vocab_options[$vocab->id()] = $vocab->label();
    }

    $form['vocabulary'] = [
      '#type' => 'select',
      '#title' => $this->t('Target Vocabulary'),
      '#description' => $this->t('Select the vocabulary where terms will be imported. Leave empty to create a new vocabulary.'),
      '#options' => $vocab_options,
      '#empty_option' => $this->t('- Create new vocabulary -'),
    ];

    $form['new_vocabulary_name'] = [
      '#type' => 'textfield',
      '#title' => $this->t('New Vocabulary Name'),
      '#description' => $this->t('Enter name for new vocabulary (required if no existing vocabulary selected).'),
      '#states' => [
        'visible' => [
          ':input[name="vocabulary"]' => ['value' => ''],
        ],
        'required' => [
          ':input[name="vocabulary"]' => ['value' => ''],
        ],
      ],
    ];

    $form['clear_existing'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Clear existing terms'),
      '#description' => $this->t('Remove all existing terms from the selected vocabulary before import.'),
      '#default_value' => FALSE,
    ];

    $form['actions'] = [
      '#type' => 'actions',
      'submit' => [
        '#type' => 'submit',
        '#value' => $this->t('Import'),
      ],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    $file_ids = $form_state->getValue('json_file');
    if (empty($file_ids)) {
      $form_state->setErrorByName('json_file', $this->t('Please select a JSON file.'));
      return;
    }

    $file = File::load($file_ids[0]);
    if (!$file) {
      $form_state->setErrorByName('json_file', $this->t('Invalid file selected.'));
      return;
    }

    // Validate JSON content
    $file_content = file_get_contents($file->getFileUri());
    $json_data = json_decode($file_content, TRUE);

    if (json_last_error() !== JSON_ERROR_NONE) {
      $form_state->setErrorByName('json_file', $this->t('Invalid JSON file: @error', ['@error' => json_last_error_msg()]));
      return;
    }

    // Validate vocabulary selection
    $vocabulary = $form_state->getValue('vocabulary');
    $new_vocab_name = $form_state->getValue('new_vocabulary_name');

    if (empty($vocabulary) && empty($new_vocab_name)) {
      $form_state->setErrorByName('new_vocabulary_name', $this->t('Please select an existing vocabulary or enter a name for a new one.'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $file_ids = $form_state->getValue('json_file');
    $file = File::load($file_ids[0]);
    $vocabulary_id = $form_state->getValue('vocabulary');
    $new_vocab_name = $form_state->getValue('new_vocabulary_name');
    $clear_existing = $form_state->getValue('clear_existing');

    try {
      // Parse JSON file
      $file_content = file_get_contents($file->getFileUri());
      $organizational_data = $this->jsonParser->parseJsonData($file_content);

      // Create or get vocabulary
      if (empty($vocabulary_id)) {
        $vocabulary_id = $this->taxonomyImporter->createVocabulary($new_vocab_name);
        $this->messenger()->addStatus($this->t('Created new vocabulary: @name', ['@name' => $new_vocab_name]));
      }

      // Clear existing terms if requested
      if ($clear_existing) {
        $this->taxonomyImporter->clearVocabularyTerms($vocabulary_id);
        $this->messenger()->addStatus($this->t('Cleared existing terms from vocabulary.'));
      }

      // Import organizational data
      $imported_count = $this->taxonomyImporter->importOrganizationalData($organizational_data, $vocabulary_id);

      $this->messenger()->addStatus($this->t('Successfully imported @count terms from the JSON file.', ['@count' => $imported_count]));

      // Delete temporary file
      $file->delete();

    } catch (\Exception $e) {
      $this->messenger()->addError($this->t('Import failed: @error', ['@error' => $e->getMessage()]));
      \Drupal::logger('org_json_importer')->error('Import failed: @error', ['@error' => $e->getMessage()]);
    }
  }

}