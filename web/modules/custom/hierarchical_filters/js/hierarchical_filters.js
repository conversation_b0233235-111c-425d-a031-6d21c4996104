(function ($, <PERSON><PERSON><PERSON>, once) {
  'use strict';

  Drupal.behaviors.hierarchicalFiltersBehavior = {
    attach: function (context, settings) {
      
      // Use Drupal's once() function on the form itself
      once('hierarchicalFiltersBehavior', 'form[id*="views-exposed-form-reglementation"], form[id*="views-exposed-form-procedure-formulaire"]', context).forEach(function (form) {
        var $form = $(form);
        
        // Try to find secteur field (can be select or radio buttons)
        var secteurSelect = $form.find('select[name="field_secteur_target_id"]');
        var secteurRadios = $form.find('input[name="field_secteur_target_id"]');

        // Try to find domaine field (can be checkboxes, select, or radio buttons)
        var domaineCheckboxes = $form.find('input[name^="field_domaine_d_activite_target_id"]');
        var domaineSelect = $form.find('select[name="field_domaines_activite_pf_target_id"]');
        var domaineRadios = $form.find('input[name="field_domaines_activite_pf_target_id"]');

        // Also try to find by fieldset or container
        var domaineFieldset = $form.find('fieldset[id*="field-domaines-activite-pf"], fieldset[id*="field-domaine-d-activite"]');

        // Determine which field types we're working with
        var usingSecteurSelect = secteurSelect.length > 0;
        var usingSecteurRadios = secteurRadios.length > 0;
        var usingDomaineCheckboxes = domaineCheckboxes.length > 0;
        var usingDomaineSelect = domaineSelect.length > 0;
        var usingDomaineRadios = domaineRadios.length > 0;
        var hasDomaineFieldset = domaineFieldset.length > 0;

        // Debug logging
        console.log('Hierarchical Filters Debug:', {
          form: $form.attr('id'),
          secteurSelect: usingSecteurSelect,
          secteurRadios: usingSecteurRadios,
          domaineCheckboxes: usingDomaineCheckboxes,
          domaineSelect: usingDomaineSelect,
          domaineRadios: usingDomaineRadios,
          domaineFieldset: hasDomaineFieldset,
          domaineFieldsetElements: domaineFieldset.length ? domaineFieldset.get(0) : null
        });

        if (!usingSecteurSelect && !usingSecteurRadios) {
          console.log('No secteur field found, exiting');
          return;
        }

        if (!usingDomaineCheckboxes && !usingDomaineSelect && !usingDomaineRadios && !hasDomaineFieldset) {
          console.log('No domaine field found, exiting');
          return;
        }
        
        // Initial setup - hide domaine container if no secteur is selected
        var initialSecteurValue = getCurrentSecteurValue();
        if (!initialSecteurValue || initialSecteurValue === 'All') {
          hideDomaineContainer();
        } else {
          loadDomainesBySecteur(initialSecteurValue);
        }
        
        // Event listeners for secteur changes
        if (usingSecteurSelect) {
          secteurSelect.on('change', handleSecteurChange);
        }
        
        if (usingSecteurRadios) {
          secteurRadios.on('change', handleSecteurChange);
        }
        
        // Function to get current secteur value
        function getCurrentSecteurValue() {
          if (usingSecteurSelect) {
            return secteurSelect.val();
          } else if (usingSecteurRadios) {
            return secteurRadios.filter(':checked').val();
          }
          return null;
        }
        
        // Function to handle secteur change
        function handleSecteurChange() {
          var secteurId = getCurrentSecteurValue();
          
          if (!secteurId || secteurId === 'All') {
            hideDomaineContainer();
          } else {
            loadDomainesBySecteur(secteurId);
          }
        }
        
        // Function to hide domaine container completely
        function hideDomaineContainer() {
          console.log('Hiding domaine container...');

          // Handle checkboxes (reglementation view)
          if (usingDomaineCheckboxes) {
            domaineCheckboxes.prop('disabled', true);
            domaineCheckboxes.prop('checked', false);

            var domaineContainer = domaineCheckboxes.closest('.form-item, .form-checkboxes');
            if (domaineContainer.length) {
              domaineContainer.hide();
              var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
              if (domaineFieldset.length) {
                domaineFieldset.hide();
              }

              // Remove any existing messages
              var messageId = 'hierarchical-filters-message';
              var existingMessage = domaineContainer.find('#' + messageId);
              if (existingMessage.length) {
                existingMessage.remove();
              }
            }
          }

          // Handle select dropdown (procedure_formulaire view)
          if (usingDomaineSelect) {
            domaineSelect.prop('disabled', true);
            domaineSelect.val('');

            var domaineContainer = domaineSelect.closest('.form-item');
            if (domaineContainer.length) {
              domaineContainer.hide();
            }
          }

          // Handle radio buttons (procedure_formulaire view alternative)
          if (usingDomaineRadios) {
            domaineRadios.prop('disabled', true);
            domaineRadios.prop('checked', false);

            var domaineContainer = domaineRadios.closest('.form-item, .form-radios');
            if (domaineContainer.length) {
              domaineContainer.hide();
            }
          }

          // Handle fieldset directly (fallback) - remove visible class
          if (hasDomaineFieldset) {
            console.log('Hiding domaine fieldset directly');
            domaineFieldset.removeClass('hierarchical-filters-visible');
          }
        }

        // Function to load domaines by secteur
        function loadDomainesBySecteur(secteurId) {
          
          // Save current domaine selections
          var selectedDomaines = [];

          if (usingDomaineCheckboxes) {
            domaineCheckboxes.filter(':checked').each(function() {
              var match = this.name.match(/\[(\d+)\]/);
              if (match) {
                selectedDomaines.push(match[1]);
              }
            });
          } else if (usingDomaineSelect) {
            var currentValue = domaineSelect.val();
            if (currentValue && currentValue !== '') {
              selectedDomaines.push(currentValue);
            }
          } else if (usingDomaineRadios) {
            var checkedRadio = domaineRadios.filter(':checked');
            if (checkedRadio.length) {
              selectedDomaines.push(checkedRadio.val());
            }
          }
          
          // Prepare domaine container and disable fields during loading
          var domaineContainer;

          if (usingDomaineCheckboxes) {
            // Hide all checkboxes immediately for better UX (no flash)
            domaineCheckboxes.each(function() {
              $(this).closest('label, .form-item').hide();
            });
            domaineCheckboxes.prop('disabled', true);

            domaineContainer = domaineCheckboxes.closest('.form-item, .form-checkboxes');
            if (domaineContainer.length) {
              domaineContainer.addClass('hierarchical-filters-loading');
              var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
              if (domaineFieldset.length) {
                domaineFieldset.addClass('hierarchical-filters-visible');
              }
            }
          } else if (usingDomaineSelect) {
            domaineSelect.prop('disabled', true);
            domaineSelect.empty().append('<option value="">Chargement...</option>');

            domaineContainer = domaineSelect.closest('.form-item');
            if (domaineContainer.length) {
              domaineContainer.addClass('hierarchical-filters-loading');
              // Also add visible class to the fieldset
              var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
              if (domaineFieldset.length) {
                domaineFieldset.addClass('hierarchical-filters-visible');
              }
            }
          } else if (usingDomaineRadios) {
            domaineRadios.prop('disabled', true);
            domaineRadios.prop('checked', false);

            domaineContainer = domaineRadios.closest('.form-item, .form-radios');
            if (domaineContainer.length) {
              domaineContainer.addClass('hierarchical-filters-loading');
              // Also add visible class to the fieldset
              var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
              if (domaineFieldset.length) {
                domaineFieldset.addClass('hierarchical-filters-visible');
              }
            }
          }

          // Handle fieldset directly if no specific field type detected
          if (hasDomaineFieldset && !domaineContainer) {
            domaineFieldset.addClass('hierarchical-filters-visible hierarchical-filters-loading');
            domaineContainer = domaineFieldset;
          }

          // Remove any existing messages
          if (domaineContainer && domaineContainer.length) {
            var messageId = 'hierarchical-filters-message';
            var existingMessage = domaineContainer.find('#' + messageId);
            if (existingMessage.length) {
              existingMessage.remove();
            }
          }

          // Déterminer le type de vue pour passer le bon paramètre
          var viewType = 'reglementation'; // Par défaut
          if (usingDomaineSelect || usingDomaineRadios) {
            viewType = 'procedure_formulaire';
          }

          $.ajax({
            url: Drupal.url('domaines-by-secteur/' + secteurId),
            type: 'GET',
            data: {
              'view_type': viewType
            },
            dataType: 'json',
            timeout: 10000,
            success: function (response) {
              console.log('AJAX Response:', response);
              console.log('Field types detected:', {
                usingDomaineCheckboxes: usingDomaineCheckboxes,
                usingDomaineSelect: usingDomaineSelect,
                usingDomaineRadios: usingDomaineRadios,
                hasDomaineFieldset: hasDomaineFieldset
              });

              // Remove loading state
              if (domaineContainer.length) {
                domaineContainer.removeClass('hierarchical-filters-loading hierarchical-filters-disabled');
              }

              var availableDomaines = [];

              if (response && response.length > 0) {
                if (usingDomaineCheckboxes) {
                  // Handle checkboxes (reglementation view)
                  response.forEach(function(domaine) {
                    availableDomaines.push(domaine.id);
                    var checkbox = domaineCheckboxes.filter('[name*="[' + domaine.id + ']"]');
                    if (checkbox.length) {
                      checkbox.prop('disabled', false);
                      checkbox.closest('label, .form-item').show();
                    }
                  });
                } else if (usingDomaineSelect) {
                  // Handle select dropdown (procedure_formulaire view)
                  domaineSelect.empty().append('<option value="">Sélectionner un domaine</option>');
                  response.forEach(function(domaine) {
                    availableDomaines.push(domaine.id);
                    domaineSelect.append('<option value="' + domaine.id + '">' + domaine.name + '</option>');
                  });
                  domaineSelect.prop('disabled', false);
                } else if (usingDomaineRadios || hasDomaineFieldset) {
                  // Handle radio buttons (procedure_formulaire view alternative)
                  var radioContainer;

                  if (hasDomaineFieldset) {
                    // Find the fieldset wrapper for radio buttons - this should be the parent container
                    radioContainer = domaineFieldset.find('.fieldset-wrapper');
                    if (radioContainer.length === 0) {
                      radioContainer = domaineFieldset;
                    }
                  } else if (usingDomaineRadios && domaineRadios.length > 0) {
                    // Find the common parent container of all radio buttons
                    radioContainer = domaineRadios.first().closest('.fieldset-wrapper, .form-radios, fieldset');
                  }

                  if (radioContainer && radioContainer.length > 0) {
                    console.log('Found radio container:', radioContainer);
                    console.log('Container type:', radioContainer[0].tagName);
                    console.log('Container classes:', radioContainer[0].className);
                    console.log('Container children count:', radioContainer.children().length);

                    // Clear ALL existing radio buttons from the entire form
                    $form.find('input[name="field_domaines_activite_pf_target_id"]').each(function() {
                      $(this).closest('.form-item').remove();
                    });

                    console.log('Container content after clearing:', radioContainer.html());

                    // Find the correct inner container for radio buttons
                    var innerRadioContainer = radioContainer.find('.form-radios');
                    if (innerRadioContainer.length === 0) {
                      innerRadioContainer = radioContainer;
                    }

                    response.forEach(function(domaine) {
                      availableDomaines.push(domaine.id);
                      var radioHtml = '<div class="form-item form-type-radio">' +
                        '<input type="radio" id="edit-field-domaines-activite-pf-target-id-' + domaine.id + '" ' +
                        'name="field_domaines_activite_pf_target_id" value="' + domaine.id + '" class="form-radio">' +
                        '<label for="edit-field-domaines-activite-pf-target-id-' + domaine.id + '">' + domaine.name + '</label>' +
                        '</div>';
                      console.log('Adding radio button to inner container:', radioHtml);
                      innerRadioContainer.append(radioHtml);
                    });

                    console.log('Radio buttons added. Inner container now contains:', innerRadioContainer.html());

                    // Update the domaineRadios reference for future use
                    domaineRadios = $form.find('input[name="field_domaines_activite_pf_target_id"]');
                    usingDomaineRadios = domaineRadios.length > 0;
                  } else {
                    console.error('Could not find radio container');
                    // Fallback: try to create radio buttons in the fieldset directly
                    if (hasDomaineFieldset) {
                      console.log('Trying fallback approach with fieldset');
                      response.forEach(function(domaine) {
                        availableDomaines.push(domaine.id);
                        var radioHtml = '<div class="form-item form-type-radio">' +
                          '<input type="radio" id="edit-field-domaines-activite-pf-target-id-' + domaine.id + '" ' +
                          'name="field_domaines_activite_pf_target_id" value="' + domaine.id + '" class="form-radio">' +
                          '<label for="edit-field-domaines-activite-pf-target-id-' + domaine.id + '">' + domaine.name + '</label>' +
                          '</div>';
                        domaineFieldset.append(radioHtml);
                      });

                      // Update the domaineRadios reference for future use
                      domaineRadios = $form.find('input[name="field_domaines_activite_pf_target_id"]');
                      usingDomaineRadios = domaineRadios.length > 0;
                    }
                  }
                }
              }

              // Restore previous selections if they are still valid
              selectedDomaines.forEach(function(domaineId) {
                if (availableDomaines.includes(domaineId)) {
                  if (usingDomaineCheckboxes) {
                    var checkbox = domaineCheckboxes.filter('[name*="[' + domaineId + ']"]');
                    if (checkbox.length) {
                      checkbox.prop('checked', true);
                    }
                  } else if (usingDomaineSelect) {
                    domaineSelect.val(domaineId);
                  } else if (usingDomaineRadios) {
                    var radio = $('input[name="field_domaines_activite_pf_target_id"][value="' + domaineId + '"]');
                    if (radio.length) {
                      radio.prop('checked', true);
                    }
                  }
                }
              });
            },
            error: function (xhr, status, error) {
              
              // Remove loading state
              if (domaineContainer.length) {
                domaineContainer.removeClass('hierarchical-filters-loading');
              }
              
              // On error, keep all checkboxes hidden and disabled
              // Don't show all checkboxes as this could be a security issue
            }
          });
        }
      });
    }
  };
})(jQuery, Drupal, once);